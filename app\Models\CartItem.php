<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cart_id',
        'component_id',
        'product_id',
        'item_type',
        'quantity',
        'price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
    ];

    /**
     * Item type constants.
     */
    public const TYPE_COMPONENT = 'component';
    public const TYPE_PRODUCT = 'product';

    /**
     * Get the cart that owns the item.
     */
    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * Get the component for the cart item.
     */
    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class);
    }

    /**
     * Get the product for the cart item.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the item (component or product) for this cart item.
     */
    public function getItemAttribute()
    {
        return $this->item_type === self::TYPE_PRODUCT ? $this->product : $this->component;
    }

    /**
     * Get the item name.
     */
    public function getItemNameAttribute(): string
    {
        $item = $this->getItemAttribute();
        return $item ? $item->name : 'Unknown Item';
    }

    /**
     * Get the item price.
     */
    public function getItemPriceAttribute(): float
    {
        $item = $this->getItemAttribute();
        if (!$item) {
            return (float) $this->price;
        }

        // For products, use effective_price if available, otherwise use price
        if ($this->item_type === self::TYPE_PRODUCT && isset($item->effective_price)) {
            return (float) $item->effective_price;
        }

        return (float) $item->price;
    }

    /**
     * Get the total price for this cart item.
     */
    public function getTotalAttribute()
    {
        return $this->price * $this->quantity;
    }

    /**
     * Update the price based on the current component price.
     */
    public function updatePrice()
    {
        if ($this->component) {
            $this->price = $this->component->price;
            $this->save();
        }
        
        return $this;
    }
}