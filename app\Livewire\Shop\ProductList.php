<?php

namespace App\Livewire\Shop;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Product;
use App\Services\SearchService;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;

class ProductList extends LivewireComponent
{
    use WithPagination;

    public $categories = [];
    public $selectedCategory = null;
    public $search = '';
    public $selectedBrands = [];
    public $priceMin = null;
    public $priceMax = null;
    public $inStockOnly = false;
    public $sortBy = 'relevance';
    public $sortDirection = 'desc';
    public $perPage = 12;
    public $availableBrands = [];
    public $selectedSpecs = [];
    public $minRating = null;
    public $availableSpecs = [];
    public $searchSuggestions = [];
    public $showSuggestions = false;
    public $priceRange = ['min' => 0, 'max' => 0];
    public $availableCategories = [];

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCategory' => ['except' => null, 'as' => 'category'],
        'selectedBrands' => ['except' => [], 'as' => 'brands'],
        'priceMin' => ['except' => null, 'as' => 'min_price'],
        'priceMax' => ['except' => null, 'as' => 'max_price'],
        'inStockOnly' => ['except' => false, 'as' => 'in_stock'],
        'sortBy' => ['except' => 'relevance', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'selectedSpecs' => ['except' => [], 'as' => 'specs'],
        'minRating' => ['except' => null, 'as' => 'rating'],
        'perPage' => ['except' => 12, 'as' => 'per_page'],
    ];

    public function mount()
    {
        try {
            $this->loadCategories();
            $this->loadFilterOptions();
        } catch (\Exception $e) {
            // Handle any database or service errors gracefully
            $this->categories = collect();
            $this->availableBrands = [];
            $this->availableSpecs = [];
            $this->priceRange = ['min' => 0, 'max' => 0];
            $this->availableCategories = [];
        }
    }

    public function loadCategories()
    {
        // Load component categories and transform to consistent format
        $componentCategories = ComponentCategory::orderBy('name')->get()
            ->map(function($category) {
                return [
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'type' => 'component'
                ];
            });

        // Get unique product categories and transform to consistent format
        $productCategories = Product::active()
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->map(function($categoryName) {
                return [
                    'name' => $categoryName,
                    'slug' => \Illuminate\Support\Str::slug($categoryName),
                    'type' => 'product'
                ];
            });

        // Merge categories as arrays (not Eloquent models)
        $this->categories = $componentCategories->concat($productCategories)->toArray();
    }

    public function loadFilterOptions()
    {
        try {
            // Get component filter options
            $searchService = app(SearchService::class);
            $searchParams = $this->getSearchParams();
            $componentFilterOptions = $searchService->getFilterOptions($searchParams);

            // Get component brands and specs
            $componentBrands = $componentFilterOptions['brands']->toArray();
            $componentSpecs = $componentFilterOptions['specifications'];
            $componentPriceRange = $componentFilterOptions['price_range'];

            // Get product filter options
            $productQuery = Product::active();
            
            // Apply current filters to get relevant options
            if ($this->search) {
                $productQuery->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            }
            
            if ($this->selectedCategory) {
                // Check if it's a product category
                $isProductCategory = Product::where('category', $this->selectedCategory)->exists();
                if ($isProductCategory) {
                    $productQuery->where('category', $this->selectedCategory);
                } else {
                    // It's a component category, so no products should match
                    $productQuery->where('id', 0);
                }
            }

            $products = $productQuery->get();
            
            // Get product brands
            $productBrands = $products->pluck('brand')->filter()->unique()->values()->toArray();
            
            // Get product specs/attributes
            $productSpecs = [];
            foreach ($products as $product) {
                if ($product->attributes && is_array($product->attributes)) {
                    foreach ($product->attributes as $key => $value) {
                        if (!isset($productSpecs[$key])) {
                            $productSpecs[$key] = [];
                        }
                        if (!in_array($value, $productSpecs[$key])) {
                            $productSpecs[$key][] = $value;
                        }
                    }
                }
            }
            
            // Get product price range
            $productPriceRange = [
                'min' => $products->min('price') ?? 0,
                'max' => $products->max('price') ?? 0
            ];

            // Merge all filter options
            $this->availableBrands = array_unique(array_merge($componentBrands, $productBrands));
            sort($this->availableBrands);
            
            $this->availableSpecs = array_merge($componentSpecs, $productSpecs);
            
            $this->priceRange = [
                'min' => min($componentPriceRange['min'], $productPriceRange['min']),
                'max' => max($componentPriceRange['max'], $productPriceRange['max'])
            ];

        } catch (\Exception $e) {
            // Set default values if service fails
            $this->availableBrands = [];
            $this->availableSpecs = [];
            $this->priceRange = ['min' => 0, 'max' => 0];
            $this->availableCategories = [];
        }
    }

    public function getComponentsProperty()
    {
        try {
            // Get components
            $searchService = app(SearchService::class);
            $searchParams = $this->getSearchParams();
            $componentQuery = $searchService->searchComponents($searchParams);
            $components = $componentQuery->get();

            // Get products and transform them to match component structure
            $productQuery = Product::active();

            // Apply category filter to products
            if ($this->selectedCategory) {
                // Check if it's a product category or component category
                $isProductCategory = Product::where('category', $this->selectedCategory)->exists();
                if ($isProductCategory) {
                    $productQuery->where('category', $this->selectedCategory);
                } else {
                    // It's a component category, exclude all products
                    $productQuery->where('id', 0);
                }
            }

            // Apply search filter to products
            if ($this->search) {
                $productQuery->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%')
                        ->orWhere('sku', 'like', '%' . $this->search . '%');
                });
            }

            // Apply brand filter to products
            if ($this->selectedBrands) {
                $productQuery->whereIn('brand', $this->selectedBrands);
            }

            // Apply price filters to products
            if ($this->priceMin) {
                $productQuery->where('price', '>=', $this->priceMin);
            }

            if ($this->priceMax) {
                $productQuery->where('price', '<=', $this->priceMax);
            }

            // Apply stock filter to products
            if ($this->inStockOnly) {
                $productQuery->where('in_stock', true)
                    ->where(function($q) {
                        $q->whereNull('stock_quantity')
                            ->orWhere('stock_quantity', '>', 0);
                    });
            }

            // Apply specifications filter to products
            if ($this->selectedSpecs) {
                foreach ($this->selectedSpecs as $specKey => $specValues) {
                    if (is_array($specValues) && !empty($specValues)) {
                        $productQuery->where(function($q) use ($specKey, $specValues) {
                            foreach ($specValues as $specValue) {
                                $q->orWhereJsonContains('attributes->' . $specKey, $specValue);
                            }
                        });
                    }
                }
            }

            // Apply rating filter to products (if you have a rating system)
            if ($this->minRating) {
                // Assuming you have a method to get average rating
                // This would need to be implemented based on your rating system
                // $productQuery->whereHas('reviews', function($q) {
                //     $q->havingRaw('AVG(rating) >= ?', [$this->minRating]);
                // });
            }

            $products = $productQuery->get();

            // Transform products to match component structure for the view
            $transformedProducts = $products->map(function ($product) {
                return (object) [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'brand' => $product->brand,
                    'model' => $product->model ?? '',
                    'price' => $product->price,
                    'stock' => $product->stock_quantity ?? 0,
                    'image' => $product->primary_image,
                    'specs' => $product->attributes ?? [],
                    'category' => (object) ['name' => $product->category ?? 'Product'],
                    'type' => 'product',
                    'created_at' => $product->created_at,
                    'average_rating' => method_exists($product, 'getAverageRating') ? $product->getAverageRating() : 0,
                    'is_featured' => $product->featured ?? false,
                    'in_stock' => $product->in_stock ?? true
                ];
            });

            // Merge components and products
            $componentsWithType = $components->map(function ($component) {
                $component->type = 'component';
                return $component;
            });

            // For better mixing, interleave components and products instead of concat
            if ($this->sortBy === 'relevance') {
                $allItems = $this->interleaveItems($componentsWithType, $transformedProducts);
            } else {
                $allItems = $componentsWithType->concat($transformedProducts);
                // Apply sorting
                $allItems = $this->applySorting($allItems);
            }

            // Manual pagination
            $currentPage = request()->get('page', 1);
            $offset = ($currentPage - 1) * $this->perPage;
            $itemsForCurrentPage = $allItems->slice($offset, $this->perPage);

            return new \Illuminate\Pagination\LengthAwarePaginator(
                $itemsForCurrentPage,
                $allItems->count(),
                $this->perPage,
                $currentPage,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        } catch (\Exception $e) {
            // Return empty pagination if service fails
            return Component::query()->where('id', 0)->paginate($this->perPage);
        }
    }

    protected function applySorting($items)
    {
        switch ($this->sortBy) {
            case 'name':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('name')
                    : $items->sortByDesc('name');
            case 'price':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('price')
                    : $items->sortByDesc('price');
            case 'stock':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('stock')
                    : $items->sortByDesc('stock');
            case 'created_at':
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('created_at')
                    : $items->sortByDesc('created_at');
            case 'rating':
                // Sort by rating if available
                return $this->sortDirection === 'asc'
                    ? $items->sortBy(function($item) {
                        return $item->average_rating ?? 0;
                    })
                    : $items->sortByDesc(function($item) {
                        return $item->average_rating ?? 0;
                    });
            case 'popularity':
                // Sort by some popularity metric (you can customize this)
                return $this->sortDirection === 'asc'
                    ? $items->sortBy('id')
                    : $items->sortByDesc('id');
            case 'relevance':
            default:
                // For relevance, items are already mixed by interleaveItems
                return $items;
        }
    }

    protected function interleaveItems($components, $products)
    {
        $result = collect();
        $componentIndex = 0;
        $productIndex = 0;
        $componentCount = $components->count();
        $productCount = $products->count();

        // Calculate ratio for interleaving (roughly 2:1 components to products)
        $componentRatio = 2;
        $productRatio = 1;

        while ($componentIndex < $componentCount || $productIndex < $productCount) {
            // Add components
            for ($i = 0; $i < $componentRatio && $componentIndex < $componentCount; $i++) {
                $result->push($components[$componentIndex]);
                $componentIndex++;
            }

            // Add products
            for ($i = 0; $i < $productRatio && $productIndex < $productCount; $i++) {
                $result->push($products[$productIndex]);
                $productIndex++;
            }
        }

        return $result;
    }

    protected function getSearchParams(): array
    {
        return [
            'search' => $this->search,
            'category' => $this->selectedCategory,
            'brands' => $this->selectedBrands,
            'price_min' => $this->priceMin,
            'price_max' => $this->priceMax,
            'specs' => $this->selectedSpecs,
            'in_stock_only' => $this->inStockOnly,
            'min_rating' => $this->minRating,
            'sort_by' => $this->sortBy,
            'sort_direction' => $this->sortDirection,
        ];
    }

    public function selectCategory($categorySlug)
    {
        $this->selectedCategory = $categorySlug;
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function clearCategory()
    {
        $this->selectedCategory = null;
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedSelectedBrands()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedSelectedCategory()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedPriceMin()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedPriceMax()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedInStockOnly()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedSortBy()
    {
        $this->resetPage();
    }

    public function updatedSortDirection()
    {
        $this->resetPage();
    }

    public function updatedSelectedSpecs()
    {
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function updatedMinRating()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function getSearchSuggestions()
    {
        if (strlen($this->search) >= 2) {
            $searchService = app(SearchService::class);
            $this->searchSuggestions = $searchService->getSearchSuggestions($this->search, 8)->toArray();
            $this->showSuggestions = true;
        } else {
            $this->searchSuggestions = [];
            $this->showSuggestions = false;
        }
    }

    public function selectSuggestion($suggestion)
    {
        $this->search = $suggestion;
        $this->showSuggestions = false;
        $this->resetPage();
    }

    public function hideSuggestions()
    {
        $this->showSuggestions = false;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedCategory = null;
        $this->selectedBrands = [];
        $this->priceMin = null;
        $this->priceMax = null;
        $this->selectedSpecs = [];
        $this->minRating = null;
        $this->inStockOnly = false;
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function removeSpecFilter($specKey, $specValue)
    {
        if (isset($this->selectedSpecs[$specKey])) {
            if (is_array($this->selectedSpecs[$specKey])) {
                $this->selectedSpecs[$specKey] = array_values(array_filter(
                    $this->selectedSpecs[$specKey],
                    fn($value) => $value !== $specValue
                ));

                if (empty($this->selectedSpecs[$specKey])) {
                    unset($this->selectedSpecs[$specKey]);
                }
            } else {
                unset($this->selectedSpecs[$specKey]);
            }
        }

        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function removeBrandFilter($brand)
    {
        $this->selectedBrands = array_values(array_filter($this->selectedBrands, fn($b) => $b !== $brand));
        $this->resetPage();
        $this->loadFilterOptions();
    }

    public function nextPage()
    {
        $this->setPage($this->getPage() + 1);
    }

    public function previousPage()
    {
        $this->setPage($this->getPage() - 1);
    }

    public function addToCart($itemId, $type = 'component')
    {
        if ($type === 'product') {
            $item = Product::find($itemId);
            $stockCheck = $item && $item->in_stock && ($item->stock_quantity ?? 0) > 0;
        } else {
            $item = Component::find($itemId);
            $stockCheck = $item && $item->stock > 0;
        }

        if (!$item || !$stockCheck) {
            session()->flash('error', 'Product is out of stock.');
            return;
        }

        try {
            // Add to cart using the CartService
            $cartService = app(\App\Services\CartService::class);
            $cartService->addToCart($item, 1);

            $this->dispatch('cartUpdated');
            session()->flash('message', 'Product added to cart successfully!');
        } catch (\InvalidArgumentException $e) {
            session()->flash('error', $e->getMessage());
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add product to cart.');
        }
    }

    public function render()
    {
        return view('livewire.shop.product-list', [
            'components' => $this->components,
        ]);
    }
}
